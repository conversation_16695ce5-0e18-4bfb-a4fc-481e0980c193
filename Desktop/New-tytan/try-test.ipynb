
from tytan import symbols, symbols_list, Compile, get_symbol_type, set_symbol_type, clear_symbol_registry
import numpy as np

if True:
    """Test basic symbol type declaration functionality."""
    print("=== Testing Symbol Type Declaration ===")
    
    # Clear registry for clean test
    clear_symbol_registry()
    
    # Test binary symbols (default)
    x = symbols('x')
    assert get_symbol_type(x) == "binary", f"Expected binary, got {get_symbol_type(x)}"
    print("✓ Default binary symbol creation works")
    
    # Test explicit binary symbols
    y = symbols('y', symbol_type="binary")
    assert get_symbol_type(y) == "binary", f"Expected binary, got {get_symbol_type(y)}"
    print("✓ Explicit binary symbol creation works")
    
    # Test Ising symbols
    s = symbols('s', symbol_type="ising")
    assert get_symbol_type(s) == "ising", f"Expected ising, got {get_symbol_type(s)}"
    print("✓ Ising symbol creation works")
    
    # Test multiple symbols
    a, b, c = symbols('a b c', symbol_type="ising")
    assert get_symbol_type(a) == "ising", f"Expected ising, got {get_symbol_type(a)}"
    assert get_symbol_type(b) == "ising", f"Expected ising, got {get_symbol_type(b)}"
    assert get_symbol_type(c) == "ising", f"Expected ising, got {get_symbol_type(c)}"
    print("✓ Multiple Ising symbols creation works")
    
    print("Symbol type declaration tests passed!\n")


from pyqubo import Spin,Array
s1, s2, s3, s4 = Spin("s1"), Spin("s2"), Spin("s3"), Spin("s4")
H = (4*s1 + 2*s2 + 7*s3 + s4)**2

model = H.compile()
qubo, offset = model.to_qubo()

offset

qubo, offset = model.to_qubo()
print(qubo) # doctest: +SKIP

numbers = [4, 2, 7, 1]

# Create Ising symbols (equivalent to PyQUBO's SPIN variables)
s = symbols_list([4], 's{}', symbol_type="ising")

print(f"Numbers: {numbers}")
print(f"Created Ising symbols: s0, s1, s2, s3")

# Build the same expression: sum(n * s for s, n in zip(s, numbers))**2
H = sum(n * s_i for s_i, n in zip(s, numbers))**2

qubo, offset = Compile(H).get_qubo()

offset

qubo

model = H.compile()
qubo, offset = model.to_qubo()

qubo

numbers = [4, 2, 7, 1]
s = Array.create('s', shape=4, vartype='SPIN')
H = sum(n * s for s, n in zip(s, numbers))**2

>>> qubo, offset = model.to_qubo()
>>> pprint(qubo) # doctest: +SKIP
{('s1', 's1'): -160.0,
 ('s1', 's2'): 64.0,
 ('s1', 's3'): 224.0,
 ('s1', 's4'): 32.0,
 ('s2', 's2'): -96.0,
 ('s2', 's3'): 112.0,
 ('s2', 's4'): 16.0,
 ('s3', 's3'): -196.0,
 ('s3', 's4'): 56.0,
 ('s4', 's4'): -52.0}
>>> print(offset)
196.0