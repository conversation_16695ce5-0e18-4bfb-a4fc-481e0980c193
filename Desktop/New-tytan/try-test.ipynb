
from tytan import symbols, symbols_list, Compile, get_symbol_type, set_symbol_type, clear_symbol_registry
import numpy as np

if True:
    """Test basic symbol type declaration functionality."""
    print("=== Testing Symbol Type Declaration ===")
    
    # Clear registry for clean test
    clear_symbol_registry()
    
    # Test binary symbols (default)
    x = symbols('x')
    assert get_symbol_type(x) == "binary", f"Expected binary, got {get_symbol_type(x)}"
    print("✓ Default binary symbol creation works")
    
    # Test explicit binary symbols
    y = symbols('y', symbol_type="binary")
    assert get_symbol_type(y) == "binary", f"Expected binary, got {get_symbol_type(y)}"
    print("✓ Explicit binary symbol creation works")
    
    # Test Ising symbols
    s = symbols('s', symbol_type="ising")
    assert get_symbol_type(s) == "ising", f"Expected ising, got {get_symbol_type(s)}"
    print("✓ Ising symbol creation works")
    
    # Test multiple symbols
    a, b, c = symbols('a b c', symbol_type="ising")
    assert get_symbol_type(a) == "ising", f"Expected ising, got {get_symbol_type(a)}"
    assert get_symbol_type(b) == "ising", f"Expected ising, got {get_symbol_type(b)}"
    assert get_symbol_type(c) == "ising", f"Expected ising, got {get_symbol_type(c)}"
    print("✓ Multiple Ising symbols creation works")
    
    print("Symbol type declaration tests passed!\n")


