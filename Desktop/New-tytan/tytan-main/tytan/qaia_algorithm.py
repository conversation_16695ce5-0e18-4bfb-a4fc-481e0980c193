import numpy as np
from scipy import sparse as sp

try:
    import torch

    assert torch.cuda.is_available()
    _INSTALL_TORCH = True
except (ImportError, AssertionError):
    _INSTALL_TORCH = False

try:
    import torch
    import torch_npu

    assert torch_npu.npu.is_available()
    _INSTALL_TORCH_NPU = True
except (ImportError, AssertionError):
    _INSTALL_TORCH_NPU = False


class QAIA:
    r"""
    The base class of QAIA.

    This class contains the basic and common functions of all the algorithms.

    Note:
        For memory efficiency, the input array 'x' is not copied and will be modified
        in-place during optimization. If you need to preserve the original data,
        please pass a copy using `x.copy()`.

    Args:
        J (Union[numpy.array, scipy.sparse.spmatrix]): The coupling matrix with shape (N x N).
        h (numpy.array): The external field with shape (N x 1).
        x (numpy.array): The initialized spin value with shape (N x batch_size).
            Will be modified during optimization. Default: ``None``.
        n_iter (int): The number of iterations. Default: ``1000``.
        batch_size (int): The number of sampling. Default: ``1``.
        backend (str): Computation backend and precision to use: 'cpu-float32','gpu-float32',
            'gpu-float16', 'gpu-int8','npu-float32'. Default: ``'cpu-float32'``.
    """

    # pylint: disable=too-many-arguments
    def __init__(self, J, h=None, x=None, n_iter=1000, batch_size=1, backend='cpu-float32'):
        """Construct a QAIA algorithm."""
        valid_backends = {'cpu-float32', 'gpu-float32', 'gpu-float16', 'gpu-int8', 'npu-float32'}
        if not isinstance(backend, str):
            raise TypeError(f"backend requires a string, but get {type(backend)}")
        if backend not in valid_backends:
            raise ValueError(f"backend must be one of {valid_backends}")
        if backend == "gpu-float32" and not _INSTALL_TORCH:
            raise ImportError("Please install pytorch before using qaia gpu backend, ensure environment has any GPU.")
        if backend == "npu-float32" and not _INSTALL_TORCH_NPU:
            raise ImportError(
                "Please install torch_npu before using qaia npu backend, ensure environment has any Ascend NPU."
            )

        if not isinstance(J, (np.ndarray, sp.spmatrix)):
            raise TypeError(f"J requires numpy.array or scipy sparse matrix, but get {type(J)}")
        if len(J.shape) != 2 or J.shape[0] != J.shape[1]:
            raise ValueError(f"J must be a square matrix, but got shape {J.shape}")

        if h is not None:
            if not isinstance(h, np.ndarray):
                raise TypeError(f"h requires numpy.array, but get {type(h)}")
            if h.shape != (J.shape[0],) and h.shape != (J.shape[0], 1):
                raise ValueError(f"h must have shape ({J.shape[0]},) or ({J.shape[0]}, 1), but got {h.shape}")
            if len(h.shape) == 1:
                h = h[:, np.newaxis]

        if x is not None:
            if not isinstance(x, np.ndarray):
                raise TypeError(f"x requires numpy.array, but get {type(x)}")
            if len(x.shape) != 2:
                raise ValueError(f"x must be a 2D array, but got shape {x.shape}")
            if x.shape[0] != J.shape[0] or x.shape[1] != batch_size:
                raise ValueError(f"x must have shape ({J.shape[0]}, {batch_size}), but got {x.shape}")

        # Replacing the imported functions with direct checks
        if not isinstance(n_iter, int):
            raise TypeError(f"n_iter requires an integer, but get {type(n_iter)}")
        if n_iter < 1:
            raise ValueError(f"n_iter should not be less than 1, but got {n_iter}")

        if not isinstance(batch_size, int):
            raise TypeError(f"batch_size requires an integer, but get {type(batch_size)}")
        if batch_size < 1:
            raise ValueError(f"batch_size should not be less than 1, but got {batch_size}")

        if backend == "gpu-float32" and _INSTALL_TORCH:
            J = torch.tensor(J).float()
            if J.layout != torch.sparse_csr:
                J = J.to("cuda")
                J = J.to_sparse_csr()
            if h is not None:
                h = torch.from_numpy(h).float().to("cuda")

        if backend == "npu-float32" and _INSTALL_TORCH_NPU:
            J = torch.tensor(J).float().to("npu")
            if h is not None:
                h = torch.from_numpy(h).float().to("npu")

        self.J = J
        self.h = h
        self.x = x
        # The number of spins
        self.N = self.J.shape[0]
        self.n_iter = n_iter
        self.batch_size = batch_size
        self.backend = backend

    def initialize(self):
        """Randomly initialize spin values."""
        if self.x is None:
            if self.backend == "cpu-float32":
                self.x = 0.02 * (np.random.rand(self.N, self.batch_size) - 0.5)
            elif self.backend == "gpu-float32":
                self.x = 0.02 * (torch.rand(self.N, self.batch_size, device="cuda") - 0.5)
            elif self.backend == "npu-float32":
                self.x = 0.02 * (torch.rand(self.N, self.batch_size).npu() - 0.5)
        else:
            if self.backend == "gpu-float32":
                self.x = torch.from_numpy(self.x).float().to("cuda")
            elif self.backend == "npu-float32":
                self.x = torch.from_numpy(self.x).float().to("npu")

    def calc_cut(self, x=None):
        r"""
        Calculate cut value.

        Args:
            x (numpy.array): The spin value with shape (N x batch_size).
                If ``None``, the initial spin will be used. Default: ``None``.
        """
        if self.backend in ["cpu-float32", 'gpu-float16', 'gpu-int8']:
            if x is None:
                sign = np.sign(self.x)
            else:
                sign = np.sign(x)
            return 0.25 * np.sum(self.J.dot(sign) * sign, axis=0) - 0.25 * self.J.sum()

        if self.backend == "gpu-float32":
            if x is None:
                sign = torch.sign(self.x)
            else:
                sign = torch.sign(x)
            return 0.25 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0) - 0.25 * self.J.sum()

        if self.backend == "npu-float32":
            if x is None:
                sign = torch.sign(self.x).npu()
            else:
                sign = torch.sign(x).npu()
            return 0.25 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0) - 0.25 * self.J.sum()

        raise ValueError("invalid backend")

    def calc_energy(self, x=None):
        r"""
        Calculate energy.

        Args:
            x (numpy.array): The spin value with shape (N x batch_size).
                If ``None``, the initial spin will be used. Default: ``None``.
        """
        if self.backend in ["cpu-float32", 'gpu-float16', 'gpu-int8']:
            if x is None:
                sign = np.sign(self.x)
            else:
                sign = np.sign(x)

            if self.h is None:
                return -0.5 * np.sum(self.J.dot(sign) * sign, axis=0)
            return -0.5 * np.sum(self.J.dot(sign) * sign, axis=0, keepdims=True) - self.h.T @ sign

        if self.backend == "gpu-float32":
            if x is None:
                sign = torch.sign(self.x)
            else:
                sign = torch.sign(x)

            if self.h is None:
                return -0.5 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0)
            return -0.5 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0, keepdim=True) - self.h.T @ sign

        if self.backend == "npu-float32":
            if x is None:
                sign = torch.sign(self.x).npu()
            else:
                sign = torch.sign(x).npu()

            if self.h is None:
                return -0.5 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0)
            return -0.5 * torch.sum(torch.sparse.mm(self.J, sign) * sign, dim=0, keepdim=True) - self.h.T @ sign

        raise ValueError("invalid backend")


class OverflowException(Exception):
    r"""
    Custom exception class for handling overflow errors in numerical calculations.

    Args:
        message: Exception message string, defaults to "Overflow error".
    """

    def __init__(self, message="Overflow error"):
        self.message = message
        super().__init__(self.message)

import numpy as np
from scipy.sparse import csr_matrix

try:
    import torch

    assert torch.cuda.is_available()
    _INSTALL_TORCH = True
except (ImportError, AssertionError):
    _INSTALL_TORCH = False






class SB(QAIA):
    r"""
    The base class of SB.

    This class is the base class for SB. It contains the initialization of
    spin values and momentum.

    Note:
        For memory efficiency, the input array 'x' is not copied and will be modified
        in-place during optimization. If you need to preserve the original data,
        please pass a copy using `x.copy()`.

    Args:
        J (Union[numpy.array, scipy.sparse.spmatrix]): The coupling matrix with shape (N x N).
        h (numpy.array): The external field with shape (N, ).
        x (numpy.array): The initialized spin value with shape (N x batch_size).
            Will be modified during optimization. If not provided (``None``), will be initialized as
            random values uniformly distributed in [-0.01, 0.01]. Default: ``None``.
        n_iter (int): The number of iterations. Default: ``1000``.
        batch_size (int): The number of sampling. Default: ``1``.
        dt (float): The step size. Default: ``1``.
        xi (float): positive constant with the dimension of frequency. Default: ``None``.
        backend (str): Computation backend and precision to use: 'cpu-float32',
            'gpu-float32','npu-float32'. Default: ``'cpu-float32'``.
    """

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        J,
        h=None,
        x=None,
        n_iter=1000,
        batch_size=1,
        dt=1,
        xi=None,
        backend='cpu-float32',
    ):
        """Construct SB algorithm."""
        super().__init__(J, h, x, n_iter, batch_size, backend)
        if self.backend == "cpu-float32":
            self.J = csr_matrix(self.J)

        # positive detuning frequency
        self.delta = 1
        self.dt = dt
        # pumping amplitude
        self.p = np.linspace(0, 1, self.n_iter)
        self.xi = xi

        if self.xi is None:
            if self.backend == "cpu-float32":
                self.xi = 0.5 * np.sqrt(self.N - 1) / np.sqrt(csr_matrix.power(self.J, 2).sum())
            elif self.backend == "gpu-float32":
                if h is not None:
                    self.xi = (
                        0.5
                        * np.sqrt(self.N - 1)
                        / torch.sqrt((self.J.to_dense() ** 2).sum() + 2 * ((self.h / 2) ** 2).sum())
                    )
                else:
                    self.xi = 0.5 * np.sqrt(self.N - 1) / torch.sqrt((self.J.to_dense() ** 2).sum())
            elif self.backend == "npu-float32":
                if h is not None:
                    self.xi = (
                        0.5
                        * np.sqrt(self.N - 1)
                        / np.sqrt(
                            csr_matrix.power(csr_matrix(self.J.cpu().numpy()), 2).sum() + 2 * ((self.h / 2) ** 2).sum()
                        )
                    )
                else:
                    self.xi = (
                        0.5 * np.sqrt(self.N - 1) / np.sqrt(csr_matrix.power(csr_matrix(self.J.cpu().numpy()), 2).sum())
                    )
        self.x = x

        self.initialize()

    def initialize(self):
        """Initialize spin values and momentum."""
        if self.backend == "cpu-float32":
            if self.x is None:
                self.x = 0.02 * (np.random.rand(self.N, self.batch_size) - 0.5)
            if self.x.shape[0] != self.N:
                raise ValueError(f"The size of x {self.x.shape[0]} is not equal to the number of spins {self.N}")
            self.y = 0.02 * (np.random.rand(self.N, self.batch_size) - 0.5)

        elif self.backend == "gpu-float32":
            if self.x is None:
                self.x = 0.02 * (torch.rand(self.N, self.batch_size, device="cuda") - 0.5)
            else:
                if isinstance(self.x, np.ndarray):
                    self.x = torch.from_numpy(self.x).float().to("cuda")
            if self.x.shape[0] != self.N:
                raise ValueError(f"The size of x {self.x.shape[0]} is not equal to the number of spins {self.N}")
            self.y = 0.02 * (torch.rand(self.N, self.batch_size, device="cuda") - 0.5)

        elif self.backend == "npu-float32":
            if self.x is None:
                self.x = 0.02 * (torch.rand(self.N, self.batch_size).npu() - 0.5)
            else:
                if isinstance(self.x, np.ndarray):
                    self.x = torch.from_numpy(self.x).float().to("npu")
            if self.x.shape[0] != self.N:
                raise ValueError(f"The size of x {self.x.shape[0]} is not equal to the number of spins {self.N}")
            self.y = 0.02 * (torch.rand(self.N, self.batch_size).npu() - 0.5)


class ASB(SB):  # noqa: N801
    r"""
    Adiabatic SB algorithm.

    Reference: `Combinatorial optimization by simulating adiabatic bifurcations in nonlinear
    Hamiltonian systems <https://www.science.org/doi/10.1126/sciadv.aav2372>`_.

    Note:
        For memory efficiency, the input array 'x' is not copied and will be modified
        in-place during optimization. If you need to preserve the original data,
        please pass a copy using `x.copy()`.

    Args:
        J (Union[numpy.array, scipy.sparse.spmatrix]): The coupling matrix with shape (N x N).
        h (numpy.array): The external field with shape (N, ).
        x (numpy.array): The initialized spin value with shape (N x batch_size).
            Will be modified during optimization. If not provided (``None``), will be initialized as
            random values uniformly distributed in [-0.01, 0.01]. Default: ``None``.
        n_iter (int): The number of iterations. Default: ``1000``.
        batch_size (int): The number of sampling. Default: ``1``.
        dt (float): The step size. Default: ``1``.
        xi (float): positive constant with the dimension of frequency. Default: ``None``.
        M (int): The number of update without mean-field terms. Default: ``2``.
        backend (str): Computation backend and precision to use: 'cpu-float32',
            'gpu-float32','npu-float32'. Default: ``'cpu-float32'``.

    Examples:
        >>> import numpy as np
        >>> from mindquantum.algorithm.qaia import ASB
        >>> J = np.array([[0, -1], [-1, 0]])
        >>> solver = ASB(J, batch_size=5)
        >>> solver.update()
        >>> print(solver.calc_cut())
        [1. 1. 1. 1. 1.]
        >>> print(solver.calc_energy())
        [-1. -1. -1. -1. -1.]
    """

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        J,
        h=None,
        x=None,
        n_iter=1000,
        batch_size=1,
        dt=1,
        xi=None,
        M=2,
        backend='cpu-float32',
    ):
        """Construct ASB algorithm."""
        super().__init__(J, h, x, n_iter, batch_size, dt, xi, backend)
        # positive Kerr coefficient
        self.K = 1
        self.M = M
        # Time step for updating without mean-field terms
        self.dm = self.dt / self.M

    def update(self):
        """Dynamical evolution based on Modified explicit symplectic Euler method."""
        # iterate on the number of MVMs
        if self.backend == "cpu-float32":
            for i in range(self.n_iter):
                for _ in range(self.M):
                    self.x += self.dm * self.y * self.delta
                    self.y -= (self.K * self.x**3 + (self.delta - self.p[i]) * self.x) * self.dm
                if self.h is None:
                    self.y += self.xi * self.dt * self.J.dot(self.x)
                else:
                    self.y += self.xi * self.dt * (self.J.dot(self.x) + self.h)

                if np.isnan(self.x).any():
                    raise OverflowException("Value is too large to handle due to large dt or xi.")

        elif self.backend == "gpu-float32":
            for i in range(self.n_iter):
                for _ in range(self.M):
                    self.x += self.dm * self.y * self.delta
                    self.y -= (self.K * self.x**3 + (self.delta - self.p[i]) * self.x) * self.dm
                if self.h is None:
                    self.y += self.xi * self.dt * torch.sparse.mm(self.J, self.x)
                else:
                    self.y += self.xi * self.dt * (torch.sparse.mm(self.J, self.x) + self.h)

                if torch.isnan(self.x).any():
                    raise OverflowException("Value is too large to handle due to large dt or xi.")

        elif self.backend == "npu-float32":
            for i in range(self.n_iter):
                for _ in range(self.M):
                    self.x += self.dm * self.y * self.delta
                    self.y -= (self.K * self.x**3 + (self.delta - self.p[i]) * self.x) * self.dm
                if self.h is None:
                    self.y += self.xi * self.dt * torch.sparse.mm(self.J, self.x)
                else:
                    self.y += self.xi * self.dt * (torch.sparse.mm(self.J, self.x) + self.h)

                if torch.isnan(self.x).any():
                    raise OverflowException("Value is too large to handle due to large dt or xi.")


class BSB(SB):  # noqa: N801
    r"""
    Ballistic SB algorithm.

    Reference: `High-performance combinatorial optimization based on classical
    mechanics <https://www.science.org/doi/10.1126/sciadv.abe7953>`_.

    Note:
        For memory efficiency, the input array 'x' is not copied and will be modified
        in-place during optimization. If you need to preserve the original data,
        please pass a copy using `x.copy()`.

        When using backend='gpu-int8', be aware that it may not perform well on dense graphs
        or graphs with continuous coefficients. Please try adjusting parameters or consider
        using 'cpu-float32' or 'gpu-float16' in these cases.

    Args:
        J (Union[numpy.array, scipy.sparse.spmatrix]): The coupling matrix with shape (N x N).
        h (numpy.array): The external field with shape (N, ).
        x (numpy.array): The initialized spin value with shape (N x batch_size).
            Will be modified during optimization. If not provided (``None``), will be initialized as
            random values uniformly distributed in [-0.01, 0.01]. Default: ``None``.
        n_iter (int): The number of iterations. Default: ``1000``.
        batch_size (int): The number of sampling. Default: ``1``.
        dt (float): The step size. Default: ``1``.
        xi (float): positive constant with the dimension of frequency. Default: ``None``.
        backend (str): Computation backend and precision to use: 'cpu-float32','gpu-float32',
            'gpu-float16', 'gpu-int8','npu-float32'. Default: ``'cpu-float32'``.

    Examples:
        >>> import numpy as np
        >>> from mindquantum.algorithm.qaia import BSB
        >>> J = np.array([[0, -1], [-1, 0]])
        >>> solver = BSB(J, batch_size=5, backend='cpu-float32')
        >>> solver.update()
        >>> print(solver.calc_cut())
        [1. 1. 1. 1. 1.]
        >>> print(solver.calc_energy())
        [-1. -1. -1. -1. -1.]
    """

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        J,
        h=None,
        x=None,
        n_iter=1000,
        batch_size=1,
        dt=1,
        xi=None,
        backend='cpu-float32',
    ):
        """Construct BSB algorithm."""
        valid_backends = {'cpu-float32', 'gpu-float32', 'gpu-float16', 'gpu-int8', 'npu-float32'}
        if not isinstance(backend, str):
            raise TypeError(f"backend requires a string, but get {type(backend)}")
        if backend not in valid_backends:
            raise ValueError(f"backend must be one of {valid_backends}")

        if backend in ['cpu-float32', 'gpu-float32', 'npu-float32']:
            super().__init__(J, h, x, n_iter, batch_size, dt, xi, backend)
        elif backend in ['gpu-float16', 'gpu-int8']:
            self.backend = backend

    def update(self):
        """Dynamical evolution based on Modified explicit symplectic Euler method."""
        # if self.backend == 'gpu-float16':
        #     if self.h is not None:
        #         h_broadcast = np.repeat(self.h, self.batch_size).reshape(self.J.shape[0], self.batch_size)
        #         _qaia_sb.bsb_update_h_half(
        #             self.J, self.x, h_broadcast, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        #     else:
        #         _qaia_sb.bsb_update_half(
        #             self.J, self.x, self.h, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        # elif self.backend == 'gpu-int8':
        #     if self.h is not None:
        #         h_broadcast = np.repeat(self.h, self.batch_size).reshape(self.J.shape[0], self.batch_size)
        #         _qaia_sb.bsb_update_h_int8(
        #             self.J, self.x, h_broadcast, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        #     else:
        #         _qaia_sb.bsb_update_int8(
        #             self.J, self.x, self.h, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        # elif self.backend == 'cpu-float32':
        if self.backend == 'cpu-float32':
            for i in range(self.n_iter):
                if self.h is None:
                    self.y += (-(self.delta - self.p[i]) * self.x + self.xi * self.J.dot(self.x)) * self.dt
                else:
                    self.y += (-(self.delta - self.p[i]) * self.x + self.xi * (self.J.dot(self.x) + self.h)) * self.dt
                self.x += self.dt * self.y * self.delta

                cond = np.abs(self.x) > 1
                self.x = np.where(cond, np.sign(self.x), self.x)
                self.y = np.where(cond, np.zeros_like(self.x), self.y)
        elif self.backend == 'gpu-float32':
            for i in range(self.n_iter):
                if self.h is None:
                    self.y += (-(self.delta - self.p[i]) * self.x + self.xi * torch.sparse.mm(self.J, self.x)) * self.dt
                else:
                    self.y += (
                        -(self.delta - self.p[i]) * self.x + self.xi * (torch.sparse.mm(self.J, self.x) + self.h)
                    ) * self.dt
                self.x += self.dt * self.y * self.delta

                cond = torch.abs(self.x) > 1
                self.x = torch.where(cond, torch.sign(self.x), self.x)
                self.y = torch.where(cond, torch.zeros_like(self.x), self.y)
        elif self.backend == 'npu-float32':
            for i in range(self.n_iter):
                if self.h is None:
                    self.y += (-(self.delta - self.p[i]) * self.x + self.xi * torch.sparse.mm(self.J, self.x)) * self.dt
                else:
                    self.y += (
                        -(self.delta - self.p[i]) * self.x + self.xi * (torch.sparse.mm(self.J, self.x) + self.h)
                    ) * self.dt
                self.x += self.dt * self.y * self.delta

                cond = torch.abs(self.x) > 1
                self.x = torch.where(cond, torch.sign(self.x), self.x)
                self.y = torch.where(cond, torch.zeros_like(self.x), self.y)


class DSB(SB):  # noqa: N801
    r"""
    Discrete SB algorithm.

    Reference: `High-performance combinatorial optimization based on classical
    mechanics <https://www.science.org/doi/10.1126/sciadv.abe7953>`_.

    Note:
        For memory efficiency, the input array 'x' is not copied and will be modified
        in-place during optimization. If you need to preserve the original data,
        please pass a copy using `x.copy()`.

        When using backend='gpu-int8', be aware that it may not perform well on dense graphs
        or graphs with continuous coefficients. Please try adjusting parameters or consider
        using 'cpu-float32' or 'gpu-float16' in these cases.

    Args:
        J (Union[numpy.array, scipy.sparse.spmatrix]): The coupling matrix with shape (N x N).
        h (numpy.array): The external field with shape (N, ).
        x (numpy.array): The initialized spin value with shape (N x batch_size).
            Will be modified during optimization. If not provided (``None``), will be initialized as
            random values uniformly distributed in [-0.01, 0.01]. Default: ``None``.
        n_iter (int): The number of iterations. Default: ``1000``.
        batch_size (int): The number of sampling. Default: ``1``.
        dt (float): The step size. Default: ``1``.
        xi (float): positive constant with the dimension of frequency. Default: ``None``.
        backend (str): Computation backend and precision to use: 'cpu-float32','gpu-float32',
            'gpu-float16', 'gpu-int8','npu-float32'. Default: ``'cpu-float32'``.

    Examples:
        >>> import numpy as np
        >>> from mindquantum.algorithm.qaia import DSB
        >>> J = np.array([[0, -1], [-1, 0]])
        >>> solver = DSB(J, batch_size=5, backend='cpu-float32')
        >>> solver.update()
        >>> print(solver.calc_cut())
        [0. 1. 1. 1. 1.]
        >>> print(solver.calc_energy())
        [ 1. -1. -1. -1. -1.]
    """

    # pylint: disable=too-many-arguments
    def __init__(
        self,
        J,
        h=None,
        x=None,
        n_iter=1000,
        batch_size=1,
        dt=1,
        xi=None,
        backend='cpu-float32',
    ):
        """Construct DSB algorithm."""
        valid_backends = {'cpu-float32', 'gpu-float32', 'gpu-float16', 'gpu-int8', 'npu-float32'}
        if not isinstance(backend, str):
            raise TypeError(f"backend requires a string, but get {type(backend)}")
        if backend not in valid_backends:
            raise ValueError(f"backend must be one of {valid_backends}")

        if backend in ['cpu-float32', 'gpu-float32', 'npu-float32']:
            super().__init__(J, h, x, n_iter, batch_size, dt, xi, backend)
        elif backend in ['gpu-float16', 'gpu-int8']:
            # super().__init__(J, h, x, n_iter, batch_size, dt, xi)
            # if not GPU_AVAILABLE:
            #     raise RuntimeError(f"GPU backend '{backend}' is not available: {GPU_DISABLE_REASON}")
            # _qaia_sb.cuda_init(self.J.shape[0], self.batch_size)
            self.backend = backend

    def update(self):
        """Dynamical evolution based on Modified explicit symplectic Euler method."""
        # if self.backend == 'gpu-float16':
        #     if self.h is not None:
        #         h_broadcast = np.repeat(self.h, self.batch_size).reshape(self.J.shape[0], self.batch_size)
        #         _qaia_sb.dsb_update_h_half(
        #             self.J, self.x, h_broadcast, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        #     else:
        #         _qaia_sb.dsb_update_half(
        #             self.J, self.x, self.h, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        # elif self.backend == 'gpu-int8':
        #     if self.h is not None:
        #         h_broadcast = np.repeat(self.h, self.batch_size).reshape(self.J.shape[0], self.batch_size)
        #         _qaia_sb.dsb_update_h_int8(
        #             self.J, self.x, h_broadcast, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        #     else:
        #         _qaia_sb.dsb_update_int8(
        #             self.J, self.x, self.h, self.batch_size, self.xi, self.delta, self.dt, self.n_iter
        #         )
        # elif self.backend == 'cpu-float32':
        if self.backend == 'cpu-float32':
            if self.h is None:
                for i in range(self.n_iter):
                    self.y += (-(self.delta - self.p[i]) * self.x + self.xi * self.J.dot(np.sign(self.x))) * self.dt
                    self.x += self.dt * self.y * self.delta

                    cond = np.abs(self.x) > 1
                    self.x = np.where(cond, np.sign(self.x), self.x)
                    self.y = np.where(cond, np.zeros_like(self.x), self.y)
            else:
                for i in range(self.n_iter):
                    self.y += (
                        -(self.delta - self.p[i]) * self.x + self.xi * (self.J.dot(np.sign(self.x)) + self.h)
                    ) * self.dt
                    self.x += self.dt * self.y * self.delta

                    cond = np.abs(self.x) > 1
                    self.x = np.where(cond, np.sign(self.x), self.x)
                    self.y = np.where(cond, np.zeros_like(self.x), self.y)
        elif self.backend == 'gpu-float32':
            if self.h is None:
                for i in range(self.n_iter):
                    self.y += (
                        -(self.delta - self.p[i]) * self.x + self.xi * torch.sparse.mm(self.J, torch.sign(self.x))
                    ) * self.dt

                    self.x += self.dt * self.y * self.delta

                    cond = torch.abs(self.x) > 1
                    self.x = torch.where(cond, torch.sign(self.x), self.x)
                    self.y = torch.where(cond, torch.zeros_like(self.y), self.y)
            else:
                for i in range(self.n_iter):
                    self.y += (
                        -(self.delta - self.p[i]) * self.x
                        + self.xi * (torch.sparse.mm(self.J, torch.sign(self.x)) + self.h)
                    ) * self.dt

                    self.x += self.dt * self.y * self.delta

                    cond = torch.abs(self.x) > 1
                    self.x = torch.where(cond, torch.sign(self.x), self.x)
                    self.y = torch.where(cond, torch.zeros_like(self.y), self.y)
        elif self.backend == 'npu-float32':
            if self.h is None:
                for i in range(self.n_iter):
                    self.y += (
                        -(self.delta - self.p[i]) * self.x + self.xi * torch.sparse.mm(self.J, torch.sign(self.x))
                    ) * self.dt

                    self.x += self.dt * self.y * self.delta

                    cond = torch.abs(self.x) > 1
                    self.x = torch.where(cond, torch.sign(self.x), self.x)
                    self.y = torch.where(cond, torch.zeros_like(self.y), self.y)
            else:
                for i in range(self.n_iter):
                    self.y += (
                        -(self.delta - self.p[i]) * self.x
                        + self.xi * (torch.sparse.mm(self.J, torch.sign(self.x)) + self.h)
                    ) * self.dt

                    self.x += self.dt * self.y * self.delta

                    cond = torch.abs(self.x) > 1
                    self.x = torch.where(cond, torch.sign(self.x), self.x)
                    self.y = torch.where(cond, torch.zeros_like(self.y), self.y)