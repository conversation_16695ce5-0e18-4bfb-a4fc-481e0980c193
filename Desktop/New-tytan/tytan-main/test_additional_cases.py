#!/usr/bin/env python3
"""
Additional test cases to verify robustness of our Ising to QUBO conversion.
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'tytan'))

from tytan import symbols, symbols_list, Compile, clear_symbol_registry
import numpy as np

def test_single_ising_variable():
    """Test with a single Ising variable."""
    print("=== Single Ising Variable Test ===")
    
    clear_symbol_registry()
    s = symbols('s', symbol_type="ising")
    
    # Simple linear term: 3*s
    H = 3 * s
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"Expression: 3*s")
    print(f"QUBO matrix: {matrix}")
    print(f"Offset: {offset}")
    
    # Manual verification: 3*s = 3*(2*x - 1) = 6*x - 3
    # Expected: coefficient of x = 6, offset = -3
    expected_coeff = 6
    expected_offset = -3
    
    actual_coeff = matrix[0, 0]
    
    if abs(actual_coeff - expected_coeff) < 1e-10 and abs(offset - expected_offset) < 1e-10:
        print("✅ Single variable test PASSED!")
        return True
    else:
        print(f"❌ Expected coeff={expected_coeff}, offset={expected_offset}")
        print(f"   Got coeff={actual_coeff}, offset={offset}")
        return False

def test_quadratic_ising_terms():
    """Test pure quadratic Ising terms."""
    print("\n=== Quadratic Ising Terms Test ===")
    
    clear_symbol_registry()
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    
    # Pure quadratic: s1 * s2
    H = s1 * s2
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"Expression: s1 * s2")
    print(f"QUBO matrix:\n{matrix}")
    print(f"Offset: {offset}")
    
    # Manual verification: s1*s2 = (2*x1-1)*(2*x2-1) = 4*x1*x2 - 2*x1 - 2*x2 + 1
    # Expected: x1 coeff = -2, x2 coeff = -2, x1*x2 coeff = 4, offset = 1
    expected_x1_coeff = -2
    expected_x2_coeff = -2
    expected_x1x2_coeff = 4
    expected_offset = 1
    
    actual_x1_coeff = matrix[0, 0]
    actual_x2_coeff = matrix[1, 1]
    actual_x1x2_coeff = matrix[0, 1]
    
    if (abs(actual_x1_coeff - expected_x1_coeff) < 1e-10 and
        abs(actual_x2_coeff - expected_x2_coeff) < 1e-10 and
        abs(actual_x1x2_coeff - expected_x1x2_coeff) < 1e-10 and
        abs(offset - expected_offset) < 1e-10):
        print("✅ Quadratic terms test PASSED!")
        return True
    else:
        print(f"❌ Expected: x1={expected_x1_coeff}, x2={expected_x2_coeff}, x1*x2={expected_x1x2_coeff}, offset={expected_offset}")
        print(f"   Got: x1={actual_x1_coeff}, x2={actual_x2_coeff}, x1*x2={actual_x1x2_coeff}, offset={offset}")
        return False

def test_constant_term():
    """Test expressions with constant terms."""
    print("\n=== Constant Term Test ===")
    
    clear_symbol_registry()
    s = symbols('s', symbol_type="ising")
    
    # Expression with constant: s + 5
    H = s + 5
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"Expression: s + 5")
    print(f"QUBO matrix: {matrix}")
    print(f"Offset: {offset}")
    
    # Manual verification: s + 5 = (2*x - 1) + 5 = 2*x + 4
    # Expected: x coeff = 2, offset = 4
    expected_coeff = 2
    expected_offset = 4
    
    actual_coeff = matrix[0, 0]
    
    if abs(actual_coeff - expected_coeff) < 1e-10 and abs(offset - expected_offset) < 1e-10:
        print("✅ Constant term test PASSED!")
        return True
    else:
        print(f"❌ Expected coeff={expected_coeff}, offset={expected_offset}")
        print(f"   Got coeff={actual_coeff}, offset={offset}")
        return False

def test_negative_coefficients():
    """Test with negative coefficients."""
    print("\n=== Negative Coefficients Test ===")
    
    clear_symbol_registry()
    s1, s2 = symbols('s1 s2', symbol_type="ising")
    
    # Expression with negative coefficients: -2*s1 + 3*s2 - s1*s2
    H = -2*s1 + 3*s2 - s1*s2
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"Expression: -2*s1 + 3*s2 - s1*s2")
    print(f"QUBO matrix:\n{matrix}")
    print(f"Offset: {offset}")
    
    # Manual verification:
    # -2*s1 = -2*(2*x1 - 1) = -4*x1 + 2
    # 3*s2 = 3*(2*x2 - 1) = 6*x2 - 3
    # -s1*s2 = -(2*x1-1)*(2*x2-1) = -(4*x1*x2 - 2*x1 - 2*x2 + 1) = -4*x1*x2 + 2*x1 + 2*x2 - 1
    # Total: (-4*x1 + 2) + (6*x2 - 3) + (-4*x1*x2 + 2*x1 + 2*x2 - 1)
    #      = -4*x1 + 6*x2 - 4*x1*x2 + 2*x1 + 2*x2 + (2 - 3 - 1)
    #      = -2*x1 + 8*x2 - 4*x1*x2 - 2
    
    expected_x1_coeff = -2
    expected_x2_coeff = 8
    expected_x1x2_coeff = -4
    expected_offset = -2
    
    actual_x1_coeff = matrix[0, 0]
    actual_x2_coeff = matrix[1, 1]
    actual_x1x2_coeff = matrix[0, 1]
    
    if (abs(actual_x1_coeff - expected_x1_coeff) < 1e-10 and
        abs(actual_x2_coeff - expected_x2_coeff) < 1e-10 and
        abs(actual_x1x2_coeff - expected_x1x2_coeff) < 1e-10 and
        abs(offset - expected_offset) < 1e-10):
        print("✅ Negative coefficients test PASSED!")
        return True
    else:
        print(f"❌ Expected: x1={expected_x1_coeff}, x2={expected_x2_coeff}, x1*x2={expected_x1x2_coeff}, offset={expected_offset}")
        print(f"   Got: x1={actual_x1_coeff}, x2={actual_x2_coeff}, x1*x2={actual_x1x2_coeff}, offset={offset}")
        return False

def test_larger_system():
    """Test with a larger system (5 variables)."""
    print("\n=== Larger System Test (5 variables) ===")
    
    clear_symbol_registry()
    s = symbols_list([5], 's{}', symbol_type="ising")
    
    # Simple sum constraint: (s0 + s1 + s2 + s3 + s4)^2
    H = (s[0] + s[1] + s[2] + s[3] + s[4])**2
    qubo, offset = Compile(H).get_qubo()
    matrix, index_map = qubo
    
    print(f"Expression: (s0 + s1 + s2 + s3 + s4)^2")
    print(f"QUBO matrix shape: {matrix.shape}")
    print(f"Offset: {offset}")
    print(f"Matrix diagonal: {np.diag(matrix)}")
    print(f"Matrix off-diagonal (first row): {matrix[0, 1:]}")
    
    # For this expression, all diagonal elements should be the same,
    # and all off-diagonal elements should be the same
    diagonal_elements = np.diag(matrix)
    off_diagonal_sample = matrix[0, 1]  # Sample off-diagonal element
    
    # Check if all diagonal elements are the same
    diagonal_consistent = np.allclose(diagonal_elements, diagonal_elements[0])
    
    # Check if off-diagonal elements are consistent (sample a few)
    off_diagonal_consistent = True
    for i in range(min(3, len(matrix))):
        for j in range(i+1, min(i+3, len(matrix))):
            if abs(matrix[i, j] - off_diagonal_sample) > 1e-10:
                off_diagonal_consistent = False
                break
    
    if diagonal_consistent and off_diagonal_consistent:
        print("✅ Larger system test PASSED! (Structure is consistent)")
        return True
    else:
        print("❌ Larger system test FAILED! (Structure inconsistent)")
        return False

def main():
    """Run all additional test cases."""
    print("🧪 Additional Test Cases for Ising to QUBO Conversion")
    print("=" * 55)
    
    tests = [
        test_single_ising_variable,
        test_quadratic_ising_terms,
        test_constant_term,
        test_negative_coefficients,
        test_larger_system
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 55)
    print(f"📊 Additional Tests Summary: {passed}/{total} passed")
    
    if passed == total:
        print("🎉 All additional tests PASSED!")
        return 0
    else:
        print("⚠️  Some additional tests FAILED.")
        return 1

if __name__ == "__main__":
    exit(main())
